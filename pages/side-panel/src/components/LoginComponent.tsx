/* eslint-disable tailwindcss/no-custom-classname */
import React, { useState, useEffect } from 'react';
import { loginStore, type DrCodeLoginData } from '@extension/storage';
import ProjectSelector from './ProjectSelector';

interface LoginComponentProps {
  onLoginSuccess?: () => void;
  onLogout?: () => void;
  isDarkMode?: boolean;
}

const LoginComponent: React.FC<LoginComponentProps> = ({ onLoginSuccess, onLogout, isDarkMode = false }) => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [userData, setUserData] = useState<DrCodeLoginData['userData'] | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [loginTabId, setLoginTabId] = useState<number | null>(null);

  useEffect(() => {
    checkLoginStatus();
  }, []);

  const checkLoginStatus = async () => {
    try {
      const loggedIn = await loginStore.isLoggedIn();
      setIsLoggedIn(loggedIn);

      if (loggedIn) {
        const user = await loginStore.getUserData();
        setUserData(user);
      }
    } catch (error) {
      console.error('Failed to check login status:', error);
    }
  };

  const handleLogin = async () => {
    setIsLoading(true);
    try {
      // Open login page in new tab
      const tab = await chrome.tabs.create({
        url: 'https://www.drcode.ai/login',
        active: true,
      });

      if (tab.id) {
        setLoginTabId(tab.id);

        // Monitor the tab for login completion
        monitorLoginTab(tab.id);
      }
    } catch (error) {
      console.error('Failed to open login page:', error);
      setIsLoading(false);
    }
  };

  const monitorLoginTab = (tabId: number) => {
    const maxAttempts = 30; // 30 seconds
    let attempts = 0;

    const checkLogin = async () => {
      attempts++;

      try {
        const results = await chrome.scripting.executeScript({
          target: { tabId },
          func: () => {
            const sessionId = localStorage.getItem('sessionId');
            const userDataStr = localStorage.getItem('userData');

            if (!sessionId || !userDataStr) {
              return null;
            }

            try {
              const userData = JSON.parse(userDataStr);
              return { sessionId, userData };
            } catch (error) {
              console.error('Failed to parse userData:', error);
              return null;
            }
          },
        });

        const result = results[0]?.result;

        if (result) {
          // Store login data
          await loginStore.setLoginData(result.sessionId, result.userData);

          // Update UI
          setIsLoggedIn(true);
          setUserData(result.userData);
          setIsLoading(false);
          setLoginTabId(null);

          // Close the login tab
          await chrome.tabs.remove(tabId);

          // Call success callback
          onLoginSuccess?.();

          return;
        }

        if (attempts >= maxAttempts) {
          console.log('Login monitoring timed out');
          setIsLoading(false);
          setLoginTabId(null);
          return;
        }

        // Check again in 1 second
        setTimeout(checkLogin, 1000);
      } catch (error) {
        console.error('Failed to check login status:', error);
        setIsLoading(false);
        setLoginTabId(null);
      }
    };

    // Start monitoring after a short delay
    setTimeout(checkLogin, 1000);
  };

  const handleLogout = async () => {
    try {
      // const headers = await loginStore.getHeaders();
      // console.log('headers', headers);
      await loginStore.clearLoginData();
      setIsLoggedIn(false);
      setUserData(null);
      onLogout?.();
    } catch (error) {
      console.error('Failed to logout:', error);
    }
  };

  if (isLoggedIn && userData) {
    return (
      <div className={`account-section ${isDarkMode ? 'dark-mode' : ''}`}>
        <div className="user-profile">
          <div className="user-info">
            <div className="user-avatar">
              {userData.orgs?.[0]?.avatar_url ? (
                <img src={userData.orgs[0].avatar_url} alt={userData.name} className="avatar-image" />
              ) : (
                <div className="avatar-placeholder">{userData.name.charAt(0).toUpperCase()}</div>
              )}
            </div>
            <div className="user-details">
              <div className="user-name">{userData.name}</div>
              <div className="user-username">@{userData.username}</div>
            </div>
          </div>
          <button onClick={handleLogout} className="logout-button">
            Logout
          </button>
        </div>

        <div className="project-section">
          <ProjectSelector isDarkMode={isDarkMode} />
        </div>
      </div>
    );
  }

  return (
    <div className={`login-container ${isDarkMode ? 'dark-mode' : ''}`}>
      <div className="login-content">
        <h3>DrCode Login</h3>
        <p>Sign in to access DrCode features</p>
        <button onClick={handleLogin} disabled={isLoading} className="login-button">
          {isLoading ? 'Opening Login Page...' : 'Login with DrCode'}
        </button>
        {isLoading && (
          <div className="login-status">
            <p>Please complete login in the opened tab</p>
            <p>This window will update automatically</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default LoginComponent;
