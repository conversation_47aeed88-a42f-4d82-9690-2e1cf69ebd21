import { useState, useEffect, useCallback } from 'react';
import { getBackendUrl } from '@extension/storage';
// import { Button } from '@extension/ui';

interface VectorDBSettingsProps {
  isDarkMode?: boolean;
}

interface DatabaseStats {
  total_vectors: number;
  dimension: number;
  index_fullness: number;
}

interface SearchResult {
  id: string;
  text: string;
  score: number;
  metadata: Record<string, any>;
}

type QualitativeScore = 'Bad' | 'Fair' | 'Good' | 'Excellent';

interface FlowScore {
  flow_name: string;
  score: number | QualitativeScore;
  ui_element_mapping: number | QualitativeScore;
  flow_coverage: number | QualitativeScore;
  action_intent_mapping: number | QualitativeScore;
  structure: number | QualitativeScore;
  ambiguity_noise: number | QualitativeScore;
  recommendations: string[];
}

interface EvaluationResult {
  success: boolean;
  overall_score?: number | QualitativeScore;
  flow_scores?: FlowScore[];
  global_recommendations?: string[];
  error?: string;
  filename?: string;
}

interface EvaluationReport {
  id: string;
  filename: string;
  timestamp: number;
  overall_score: number | QualitativeScore;
  summary: string;
  result: EvaluationResult;
}

export const VectorDBSettings = ({ isDarkMode = false }: VectorDBSettingsProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState<DatabaseStats | null>(null);

  // Document management
  const [newDocumentText, setNewDocumentText] = useState('');
  const [documentMetadata, setDocumentMetadata] = useState('{}');
  const [isAddingDocument, setIsAddingDocument] = useState(false);

  // Search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);

  // File upload
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [dragActive, setDragActive] = useState(false);

  // Document evaluation
  const [isEvaluating, setIsEvaluating] = useState(false);
  const [evaluationResult, setEvaluationResult] = useState<EvaluationResult | null>(null);
  const [evaluationReports, setEvaluationReports] = useState<EvaluationReport[]>([]);
  const [showEvaluationModal, setShowEvaluationModal] = useState(false);
  const [selectedReport, setSelectedReport] = useState<EvaluationReport | null>(null);

  // Check backend connection
  const checkConnection = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const backendUrl = await getBackendUrl();
      const response = await fetch(`${backendUrl}/api/v1/health`);
      if (response.ok) {
        setIsConnected(true);
        // Get database stats
        const statsResponse = await fetch(`${backendUrl}/api/v1/stats`);
        if (statsResponse.ok) {
          const statsData = await statsResponse.json();
          setStats(statsData);
        }
      } else {
        setIsConnected(false);
        setError('Backend server not responding');
      }
    } catch (err) {
      setIsConnected(false);
      setError(err instanceof Error ? err.message : 'Connection failed');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Save evaluation reports to localStorage
  const saveEvaluationReports = useCallback((reports: EvaluationReport[]) => {
    try {
      localStorage.setItem('vectordb-evaluation-reports', JSON.stringify(reports));
      setEvaluationReports(reports);
    } catch (error) {
      console.error('Failed to save evaluation reports:', error);
    }
  }, []);

  // Generate summary from evaluation result
  const generateSummary = (result: EvaluationResult): string => {
    if (!result.success || !result.flow_scores) return 'Evaluation failed';

    const flowCount = result.flow_scores.length;
    const avgScore = result.overall_score || 0;
    const majorIssues = result.global_recommendations?.length || 0;

    const scoreText = isQualitativeScore(avgScore) ? avgScore : `${avgScore.toFixed(1)}%`;
    return `${flowCount} flow${flowCount !== 1 ? 's' : ''} evaluated, ${scoreText} overall score, ${majorIssues} recommendation${majorIssues !== 1 ? 's' : ''}`;
  };

  // Add document
  const addDocument = useCallback(async () => {
    if (!newDocumentText.trim()) return;

    setIsAddingDocument(true);
    setError(null);

    try {
      let metadata = {};
      try {
        metadata = JSON.parse(documentMetadata);
      } catch {
        metadata = {};
      }

      const backendUrl = await getBackendUrl();
      const response = await fetch(`${backendUrl}/api/v1/documents`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: newDocumentText,
          metadata: metadata,
        }),
      });

      if (response.ok) {
        setNewDocumentText('');
        setDocumentMetadata('{}');
        await checkConnection(); // Refresh stats
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to add document');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add document');
    } finally {
      setIsAddingDocument(false);
    }
  }, [newDocumentText, documentMetadata, checkConnection]);

  // Search documents
  const searchDocuments = useCallback(async () => {
    if (!searchQuery.trim()) return;

    setIsSearching(true);
    setError(null);

    try {
      const backendUrl = await getBackendUrl();
      const response = await fetch(`${backendUrl}/api/v1/search`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: searchQuery,
          top_k: 5,
        }),
      });

      if (response.ok) {
        const data = await response.json();
        setSearchResults(data.results || []);
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Search failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed');
    } finally {
      setIsSearching(false);
    }
  }, [searchQuery]);

  // Upload file with evaluation
  const uploadFile = useCallback(async () => {
    if (!selectedFile) return;

    setIsUploading(true);
    setError(null);

    try {
      // First evaluate the document
      setIsEvaluating(true);
      const formData = new FormData();
      formData.append('file', selectedFile);

      const backendUrl = await getBackendUrl();
      const evaluationResponse = await fetch(`${backendUrl}/api/v1/evaluate-document-file`, {
        method: 'POST',
        body: formData,
      });

      let evaluationResult: EvaluationResult | null = null;
      if (evaluationResponse.ok) {
        evaluationResult = await evaluationResponse.json();
      }

      setIsEvaluating(false);

      // Then upload to vector database
      const uploadFormData = new FormData();
      uploadFormData.append('file', selectedFile);
      uploadFormData.append('metadata', JSON.stringify({ filename: selectedFile.name }));

      const uploadResponse = await fetch(`${backendUrl}/api/v1/documents/upload`, {
        method: 'POST',
        body: uploadFormData,
      });

      if (uploadResponse.ok) {
        // Save evaluation report if evaluation was successful
        if (evaluationResult && evaluationResult.success) {
          const report: EvaluationReport = {
            id: Date.now().toString(),
            filename: selectedFile.name,
            timestamp: Date.now(),
            overall_score: evaluationResult.overall_score || 'Bad',
            summary: generateSummary(evaluationResult),
            result: evaluationResult,
          };

          const updatedReports = [report, ...evaluationReports];
          saveEvaluationReports(updatedReports);
          setEvaluationResult(evaluationResult);
        }

        setSelectedFile(null);
        await checkConnection(); // Refresh stats
      } else {
        const errorData = await uploadResponse.json();
        setError(errorData.detail || 'File upload failed');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'File upload failed');
    } finally {
      setIsUploading(false);
      setIsEvaluating(false);
    }
  }, [selectedFile, checkConnection, evaluationReports, saveEvaluationReports]);

  // Handle drag and drop
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      const allowedTypes = ['text/plain', 'text/markdown'];
      const allowedExtensions = ['.txt', '.md', '.docx'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (allowedTypes.includes(file.type) || allowedExtensions.includes(fileExtension)) {
        setSelectedFile(file);
      } else {
        setError('Please upload a .txt, .md, or .docx file');
      }
    }
  }, []);

  // View evaluation report
  const viewReport = useCallback((report: EvaluationReport) => {
    setSelectedReport(report);
    setShowEvaluationModal(true);
  }, []);

  const isQualitativeScore = (score: number | QualitativeScore): score is QualitativeScore => {
    return typeof score === 'string';
  };

  const getQualitativeScoreColor = (score: QualitativeScore) => {
    switch (score) {
      case 'Excellent':
        return isDarkMode ? 'text-green-400' : 'text-green-600';
      case 'Good':
        return isDarkMode ? 'text-green-300' : 'text-green-500';
      case 'Fair':
        return isDarkMode ? 'text-yellow-400' : 'text-yellow-600';
      case 'Bad':
        return isDarkMode ? 'text-red-400' : 'text-red-600';
      default:
        return isDarkMode ? 'text-gray-400' : 'text-gray-500';
    }
  };

  // Get score color
  const getScoreColor = (score: number | QualitativeScore, maxScore: number = 100) => {
    if (isQualitativeScore(score)) {
      return getQualitativeScoreColor(score);
    }
    const percentage = (score / maxScore) * 100;
    if (percentage >= 80) return isDarkMode ? 'text-green-400' : 'text-green-600';
    if (percentage >= 60) return isDarkMode ? 'text-yellow-400' : 'text-yellow-600';
    return isDarkMode ? 'text-red-400' : 'text-red-600';
  };

  const getOverallScoreColor = (score: number | QualitativeScore) => {
    if (isQualitativeScore(score)) {
      return getQualitativeScoreColor(score);
    }
    if (score >= 80) return isDarkMode ? 'text-green-400' : 'text-green-600';
    if (score >= 60) return isDarkMode ? 'text-yellow-400' : 'text-yellow-600';
    return isDarkMode ? 'text-red-400' : 'text-red-600';
  };

  const formatScore = (score: number | QualitativeScore, maxScore?: number) => {
    if (isQualitativeScore(score)) {
      return score;
    }
    return maxScore ? `${score.toFixed(1)}/${maxScore}` : `${score.toFixed(1)}/100`;
  };

  const getScoreThreshold = (score: number | QualitativeScore, threshold: number) => {
    if (isQualitativeScore(score)) {
      // Map qualitative scores to numerical equivalents for threshold comparison
      const scoreMap = { Bad: 40, Fair: 60, Good: 80, Excellent: 95 };
      return scoreMap[score] >= threshold;
    }
    return score >= threshold;
  };

  // Clear evaluation history
  const clearEvaluationHistory = useCallback(() => {
    if (!confirm('Are you sure you want to clear all evaluation history? This action cannot be undone.')) {
      return;
    }

    try {
      localStorage.removeItem('vectordb-evaluation-reports');
      setEvaluationReports([]);
      setEvaluationResult(null);
    } catch (error) {
      console.error('Failed to clear evaluation history:', error);
      setError('Failed to clear evaluation history');
    }
  }, []);

  // Clear database
  const clearDatabase = useCallback(async () => {
    if (
      !confirm(
        'Are you sure you want to clear all documents from the database AND all evaluation history? This action cannot be undone.',
      )
    ) {
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const backendUrl = await getBackendUrl();
      const response = await fetch(`${backendUrl}/api/v1/clear`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await checkConnection(); // Refresh stats
        setSearchResults([]);

        // Also clear evaluation history when clearing vector database
        try {
          localStorage.removeItem('vectordb-evaluation-reports');
          setEvaluationReports([]);
          setEvaluationResult(null);
        } catch (error) {
          console.error('Failed to clear evaluation history:', error);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.detail || 'Failed to clear database');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to clear database');
    } finally {
      setIsLoading(false);
    }
  }, [checkConnection]);

  // Check connection on mount and when URL changes
  useEffect(() => {
    checkConnection();
  }, [checkConnection]);

  // Load evaluation reports from localStorage
  useEffect(() => {
    const savedReports = localStorage.getItem('vectordb-evaluation-reports');
    if (savedReports) {
      try {
        setEvaluationReports(JSON.parse(savedReports));
      } catch (error) {
        console.error('Failed to load evaluation reports:', error);
      }
    }
  }, []);

  return (
    <div className="space-y-6 p-6">
      <div>
        <h2 className={`mb-4 text-2xl font-bold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          Vector Database Management
        </h2>
        <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
          Manage your Pinecone vector database for prompt refinement with document evaluation
        </p>
      </div>

      {/* Backend Connection */}
      <div
        className={`rounded-lg border p-4 ${isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-white'}`}>
        <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
          Backend Connection
        </h3>

        <div className="space-y-3">
          <div className="flex items-center gap-3">
            <button
              onClick={checkConnection}
              disabled={isLoading}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                isDarkMode ? 'bg-[#7c3aed] hover:bg-[#6d28d9]' : 'bg-[#875bf8] hover:bg-[#7c3aed]'
              } text-white disabled:opacity-50 disabled:cursor-not-allowed`}>
              {isLoading ? 'Checking...' : 'Test Connection'}
            </button>

            <div className="flex items-center gap-2">
              <div className={`h-3 w-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-red-600 bg-red-900/20' : 'border-red-200 bg-red-50'}`}>
          <p className={`text-sm ${isDarkMode ? 'text-red-400' : 'text-red-700'}`}>{error}</p>
        </div>
      )}

      {/* Database Stats */}
      {isConnected && stats && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Database Statistics
          </h3>

          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-[#875bf8]' : 'text-[#875bf8]'}`}>
                {stats.total_vectors.toLocaleString()}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Total Vectors</div>
            </div>

            <div className="text-center">
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                {stats.dimension}
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Dimensions</div>
            </div>

            <div className="text-center">
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>
                {(stats.index_fullness * 100).toFixed(1)}%
              </div>
              <div className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>Index Fullness</div>
            </div>
          </div>
        </div>
      )}

      {/* Add Document */}
      {isConnected && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Add Document
          </h3>

          <div className="space-y-3">
            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Document Text
              </label>
              <textarea
                value={newDocumentText}
                onChange={e => setNewDocumentText(e.target.value)}
                rows={4}
                className={`mt-1 block w-full rounded-md border px-3 py-2 ${
                  isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22] text-gray-200' : 'border-gray-300 bg-white text-gray-900'
                }`}
                placeholder="Enter document text..."
              />
            </div>

            <div>
              <label className={`block text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Metadata (JSON)
              </label>
              <input
                type="text"
                value={documentMetadata}
                onChange={e => setDocumentMetadata(e.target.value)}
                className={`mt-1 block w-full rounded-md border px-3 py-2 ${
                  isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22] text-gray-200' : 'border-gray-300 bg-white text-gray-900'
                }`}
                placeholder='{"category": "example", "source": "manual"}'
              />
            </div>

            <button
              onClick={addDocument}
              disabled={isAddingDocument || !newDocumentText.trim()}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                isDarkMode ? 'bg-green-600 hover:bg-green-700' : 'bg-green-500 hover:bg-green-600'
              } text-white disabled:opacity-50 disabled:cursor-not-allowed`}>
              {isAddingDocument ? 'Adding...' : 'Add Document'}
            </button>
          </div>
        </div>
      )}

      {/* Enhanced File Upload with Evaluation */}
      {isConnected && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            📄 Upload & Evaluate Documents
          </h3>
          <p className={`text-sm mb-4 ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
            Upload documents to add them to the vector database. Each document will be automatically evaluated for UI
            testing quality.
          </p>

          <div className="space-y-4">
            {/* Drag & Drop Area */}
            <div
              className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive
                  ? isDarkMode
                    ? 'border-[#875bf8] bg-[#875bf8]/20'
                    : 'border-[#875bf8] bg-[#875bf8]/10'
                  : isDarkMode
                    ? 'border-[#2e2e60] bg-[#0d0d22]/50'
                    : 'border-gray-300 bg-gray-50'
              } ${isUploading || isEvaluating ? 'pointer-events-none opacity-50' : 'cursor-pointer'}`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={() => document.getElementById('file-input-vector')?.click()}>
              <input
                id="file-input-vector"
                type="file"
                accept=".txt,.md,.docx"
                onChange={e => setSelectedFile(e.target.files?.[0] || null)}
                className="hidden"
                disabled={isUploading || isEvaluating}
              />

              {isUploading || isEvaluating ? (
                <div className="flex flex-col items-center">
                  <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#875bf8] mb-4"></div>
                  <p className="text-lg font-medium">{isEvaluating ? 'Evaluating Document...' : 'Uploading...'}</p>
                  <p className="text-sm opacity-75 mt-2">
                    {isEvaluating ? 'This may take a few moments' : 'Adding to vector database'}
                  </p>
                </div>
              ) : (
                <div>
                  <div className="text-6xl mb-4">📤</div>
                  <p className="text-lg font-medium mb-2">
                    {dragActive ? 'Drop your document here' : 'Upload a document for evaluation'}
                  </p>
                  <p className="text-sm opacity-75">Supported formats: .txt, .md, .docx</p>
                  {selectedFile && (
                    <p className={`text-sm mt-2 font-medium ${isDarkMode ? 'text-[#875bf8]' : 'text-[#875bf8]'}`}>
                      Selected: {selectedFile.name}
                    </p>
                  )}
                </div>
              )}
            </div>

            {/* Upload Button */}
            {selectedFile && !isUploading && !isEvaluating && (
              <button
                onClick={uploadFile}
                className={`w-full py-3 px-4 rounded-lg font-medium transition-colors ${
                  isDarkMode ? 'bg-[#7c3aed] hover:bg-[#6d28d9]' : 'bg-[#875bf8] hover:bg-[#7c3aed]'
                } text-white`}>
                🚀 Upload & Evaluate Document
              </button>
            )}
          </div>
        </div>
      )}

      {/* Evaluation Results (Latest) */}
      {evaluationResult && evaluationResult.success && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-green-600 bg-green-900/20' : 'border-green-200 bg-green-50'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-green-400' : 'text-green-700'}`}>
            ✅ Latest Evaluation Results
          </h3>

          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <span className="text-sm opacity-75">Overall Score</span>
              <div className={`text-2xl font-bold ${getOverallScoreColor(evaluationResult.overall_score || 0)}`}>
                {evaluationResult.overall_score ? formatScore(evaluationResult.overall_score) : 'N/A'}
              </div>
            </div>
            <div>
              <span className="text-sm opacity-75">Flows Evaluated</span>
              <div className={`text-2xl font-bold ${isDarkMode ? 'text-[#875bf8]' : 'text-[#875bf8]'}`}>
                {evaluationResult.flow_scores?.length || 0}
              </div>
            </div>
          </div>

          <button
            onClick={() => {
              const latestReport = evaluationReports[0];
              if (latestReport) viewReport(latestReport);
            }}
            className={`text-sm px-3 py-1 rounded ${
              isDarkMode ? 'bg-[#7c3aed] hover:bg-[#6d28d9]' : 'bg-[#875bf8] hover:bg-[#7c3aed]'
            } text-white`}>
            👁️ View Full Report
          </button>
        </div>
      )}

      {/* Evaluation Reports History */}
      {evaluationReports.length > 0 && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-white'}`}>
          <div className="flex items-center justify-between mb-3">
            <h3 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              📊 Evaluation History
            </h3>
            <button
              onClick={clearEvaluationHistory}
              className={`px-3 py-1 text-sm rounded font-medium transition-colors ${
                isDarkMode ? 'bg-red-600 hover:bg-red-700 text-white' : 'bg-red-600 hover:bg-red-700 text-white'
              }`}
              title="Clear all evaluation history">
              🗑️ Clear History
            </button>
          </div>

          <div className="space-y-2">
            {evaluationReports.slice(0, 5).map(report => (
              <div
                key={report.id}
                className={`p-3 rounded border ${
                  isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-gray-50'
                }`}>
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <span className={`font-medium ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                        {report.filename}
                      </span>
                      <span className={`text-lg font-bold ${getOverallScoreColor(report.overall_score)}`}>
                        {formatScore(report.overall_score)}
                      </span>
                    </div>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>{report.summary}</p>
                    <p className={`text-xs ${isDarkMode ? 'text-gray-500' : 'text-gray-500'}`}>
                      {new Date(report.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => viewReport(report)}
                      className={`px-3 py-1 text-sm rounded ${
                        isDarkMode ? 'bg-gray-600 hover:bg-gray-500' : 'bg-gray-200 hover:bg-gray-300'
                      } transition-colors`}
                      title="View report details">
                      👁️ View
                    </button>
                  </div>
                </div>
              </div>
            ))}

            {evaluationReports.length > 5 && (
              <p className={`text-sm text-center ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                And {evaluationReports.length - 5} more reports...
              </p>
            )}
          </div>
        </div>
      )}

      {/* Search Documents */}
      {isConnected && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-white'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
            Search Documents
          </h3>

          <div className="space-y-3">
            <div className="flex gap-2">
              <input
                type="text"
                value={searchQuery}
                onChange={e => setSearchQuery(e.target.value)}
                className={`flex-1 rounded-md border px-3 py-2 ${
                  isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22] text-gray-200' : 'border-gray-300 bg-white text-gray-900'
                }`}
                placeholder="Enter search query..."
                onKeyDown={e => e.key === 'Enter' && searchDocuments()}
              />
              <button
                onClick={searchDocuments}
                disabled={isSearching || !searchQuery.trim()}
                className={`px-4 py-2 rounded-md font-medium transition-colors ${
                  isDarkMode ? 'bg-purple-600 hover:bg-purple-700' : 'bg-purple-500 hover:bg-purple-600'
                } text-white disabled:opacity-50 disabled:cursor-not-allowed`}>
                {isSearching ? 'Searching...' : 'Search'}
              </button>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="space-y-2">
                <h4 className={`text-sm font-medium ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  Search Results ({searchResults.length})
                </h4>
                {searchResults.map(result => (
                  <div
                    key={result.id}
                    className={`rounded border p-3 ${
                      isDarkMode ? 'border-[#2e2e60] bg-[#0d0d22]' : 'border-gray-200 bg-gray-50'
                    }`}>
                    <div className="flex items-center justify-between mb-2">
                      <span className={`text-xs font-mono ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                        ID: {result.id.substring(0, 8)}...
                      </span>
                      <span className={`text-xs ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                        Score: {result.score.toFixed(3)}
                      </span>
                    </div>
                    <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                      {result.text.substring(0, 200)}
                      {result.text.length > 200 && '...'}
                    </p>
                    {Object.keys(result.metadata).length > 0 && (
                      <div className="mt-2">
                        <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                          Metadata: {JSON.stringify(result.metadata)}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Danger Zone */}
      {isConnected && (
        <div
          className={`rounded-lg border p-4 ${isDarkMode ? 'border-red-600 bg-red-900/20' : 'border-red-200 bg-red-50'}`}>
          <h3 className={`mb-3 text-lg font-semibold ${isDarkMode ? 'text-red-400' : 'text-red-700'}`}>Danger Zone</h3>

          <div className="space-y-3">
            <p className={`text-sm ${isDarkMode ? 'text-red-300' : 'text-red-600'}`}>
              Clear all documents from the vector database and all evaluation history. This action cannot be undone.
            </p>

            <button
              onClick={clearDatabase}
              disabled={isLoading}
              className="px-4 py-2 rounded-md font-medium transition-colors bg-red-600 hover:bg-red-700 text-white disabled:opacity-50 disabled:cursor-not-allowed">
              {isLoading ? 'Clearing...' : 'Clear Database & History'}
            </button>
          </div>
        </div>
      )}

      {/* Evaluation Modal */}
      {showEvaluationModal && selectedReport && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div
            className={`max-w-4xl w-full max-h-[90vh] rounded-lg ${
              isDarkMode ? 'bg-[#0d0d22]' : 'bg-white'
            } overflow-hidden`}>
            {/* Modal Header */}
            <div
              className={`flex items-center justify-between p-6 border-b ${
                isDarkMode ? 'border-[#2e2e60]' : 'border-gray-200'
              }`}>
              <div>
                <h2 className={`text-xl font-semibold ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
                  📊 Document Evaluation Report
                </h2>
                <p className={`text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  {selectedReport.filename} • {new Date(selectedReport.timestamp).toLocaleString()}
                </p>
              </div>
              <button
                onClick={() => setShowEvaluationModal(false)}
                className={`p-2 rounded-full hover:bg-opacity-10 hover:bg-gray-500 ${
                  isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-black'
                }`}>
                ✕
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-80px)]">
              {selectedReport.result.success ? (
                <>
                  {/* Overall Score */}
                  <div className={`p-6 rounded-lg mb-6 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-xl font-semibold">Overall Evaluation Score</h3>
                    </div>
                    <div className="flex items-center">
                      <span
                        className={`text-4xl font-bold ${
                          selectedReport.result.overall_score
                            ? getOverallScoreColor(selectedReport.result.overall_score)
                            : 'text-gray-400'
                        }`}>
                        {selectedReport.result.overall_score ? formatScore(selectedReport.result.overall_score) : 'N/A'}
                      </span>
                      {!isQualitativeScore(selectedReport.result.overall_score || 0) && (
                        <span className="text-2xl ml-2 opacity-75">/ 100</span>
                      )}
                      <span
                        className={`ml-4 text-2xl ${
                          selectedReport.result.overall_score &&
                          getScoreThreshold(selectedReport.result.overall_score, 70)
                            ? 'text-green-500'
                            : 'text-yellow-500'
                        }`}>
                        {selectedReport.result.overall_score &&
                        getScoreThreshold(selectedReport.result.overall_score, 70)
                          ? '✅'
                          : '⚠️'}
                      </span>
                    </div>
                  </div>

                  {/* Flow Scores */}
                  {selectedReport.result.flow_scores && selectedReport.result.flow_scores.length > 0 && (
                    <div className="mb-6">
                      <h3 className="text-lg font-semibold mb-4">Individual Flow Scores</h3>
                      <div className="space-y-4">
                        {selectedReport.result.flow_scores.map((flow, index) => (
                          <div
                            key={index}
                            className={`p-4 rounded-lg border ${
                              isDarkMode ? 'border-gray-600 bg-gray-750' : 'border-gray-200 bg-gray-50'
                            }`}>
                            <div className="flex items-center justify-between mb-3">
                              <h4 className="font-medium text-lg">{flow.flow_name}</h4>
                              <span className={`text-xl font-semibold ${getOverallScoreColor(flow.score)}`}>
                                {formatScore(flow.score)}
                              </span>
                            </div>

                            {/* Detailed Scores */}
                            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-4">
                              <div>
                                <span className="text-sm opacity-75">UI Element Mapping</span>
                                <div className={`font-medium ${getScoreColor(flow.ui_element_mapping, 30)}`}>
                                  {formatScore(flow.ui_element_mapping, 30)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Flow Coverage</span>
                                <div className={`font-medium ${getScoreColor(flow.flow_coverage, 30)}`}>
                                  {formatScore(flow.flow_coverage, 30)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Action-Intent Mapping</span>
                                <div className={`font-medium ${getScoreColor(flow.action_intent_mapping, 20)}`}>
                                  {formatScore(flow.action_intent_mapping, 20)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Structure</span>
                                <div className={`font-medium ${getScoreColor(flow.structure, 15)}`}>
                                  {formatScore(flow.structure, 15)}
                                </div>
                              </div>
                              <div>
                                <span className="text-sm opacity-75">Ambiguity/Noise</span>
                                <div className={`font-medium ${getScoreColor(flow.ambiguity_noise, 5)}`}>
                                  {formatScore(flow.ambiguity_noise, 5)}
                                </div>
                              </div>
                            </div>

                            {/* Recommendations */}
                            {flow.recommendations && flow.recommendations.length > 0 && (
                              <div>
                                <h5 className="font-medium mb-2 text-sm opacity-75">Recommendations:</h5>
                                <ul className="space-y-1 text-sm">
                                  {flow.recommendations.map((rec, recIndex) => (
                                    <li key={recIndex} className="flex items-start">
                                      <span className="text-[#875bf8] mr-2">•</span>
                                      <span>{rec}</span>
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Global Recommendations */}
                  {selectedReport.result.global_recommendations &&
                    selectedReport.result.global_recommendations.length > 0 && (
                      <div
                        className={`p-4 rounded-lg ${
                          isDarkMode
                            ? 'bg-[#875bf8]/20 border border-[#875bf8]'
                            : 'bg-[#875bf8]/10 border border-[#875bf8]'
                        }`}>
                        <h4 className={`font-medium mb-3 ${isDarkMode ? 'text-[#875bf8]' : 'text-blue-700'}`}>
                          🎯 Global Recommendations
                        </h4>
                        <ul className="space-y-2 text-sm">
                          {selectedReport.result.global_recommendations.map((rec, index) => (
                            <li key={index} className="flex items-start">
                              <span className="text-[#875bf8] mr-2">•</span>
                              <span>{rec}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                </>
              ) : (
                <div
                  className={`p-4 rounded-lg ${
                    isDarkMode ? 'bg-red-900/20 border border-red-600' : 'bg-red-50 border border-red-200'
                  }`}>
                  <div className="flex items-center mb-2">
                    <span className="text-red-500 mr-2 text-xl">⚠️</span>
                    <h3 className="font-semibold text-red-600">Evaluation Failed</h3>
                  </div>
                  <p className="text-red-600">
                    {selectedReport.result.error || 'An unknown error occurred during evaluation.'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
