/* eslint-disable tailwindcss/no-custom-classname */
import React, { useState, useEffect } from 'react';
import { loginStore, type Project } from '@extension/storage';

interface ProjectSelectorProps {
  isDarkMode?: boolean;
}

const ProjectSelector: React.FC<ProjectSelectorProps> = ({ isDarkMode = false }) => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [selectedProjectId, setSelectedProjectId] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadProjects();
    loadSelectedProject();
  }, []);

  const loadProjects = async () => {
    try {
      const userData = await loginStore.getUserData();
      if (!userData) {
        setError('User data not available');
        return;
      }

      setIsLoading(true);
      setError(null);

      const response = await fetch(`https://devapi.drcode.ai/testgpt/api/projects/user/${userData.user_id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch projects: ${response.status}`);
      }

      const data = await response.json();

      if (data.success) {
        setProjects(data.data);
        await loginStore.setProjects(data.data);
      } else {
        throw new Error(data.message || 'Failed to fetch projects');
      }
    } catch (err) {
      console.error('Error fetching projects:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch projects');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSelectedProject = async () => {
    try {
      const projectId = await loginStore.getSelectedProjectId();
      setSelectedProjectId(projectId);
    } catch (error) {
      console.error('Error loading selected project:', error);
    }
  };

  const handleProjectSelect = async (projectId: number) => {
    try {
      setSelectedProjectId(projectId);
      await loginStore.setSelectedProject(projectId);
    } catch (error) {
      console.error('Error setting selected project:', error);
      setError('Failed to save project selection');
    }
  };

  if (isLoading) {
    return (
      <div className={`project-selector ${isDarkMode ? 'dark-mode' : ''}`}>
        <h3>Projects</h3>
        <div className="loading-state">
          <div className="spinner"></div>
          <p>Loading projects...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`project-selector ${isDarkMode ? 'dark-mode' : ''}`}>
        <h3>Projects</h3>
        <div className="error-state">
          <p className="error-message">{error}</p>
          <button onClick={loadProjects} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  if (projects.length === 0) {
    return (
      <div className={`project-selector ${isDarkMode ? 'dark-mode' : ''}`}>
        <h3>Projects</h3>
        <div className="empty-state">
          <p>No projects found</p>
          <button onClick={loadProjects} className="retry-button">
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`project-selector ${isDarkMode ? 'dark-mode' : ''}`}>
      <h3>Select Project</h3>
      <div className="project-list">
        {projects.map(project => (
          <button
            key={project.id}
            className={`project-item ${selectedProjectId === project.id ? 'selected' : ''}`}
            onClick={() => handleProjectSelect(project.id)}
            onKeyDown={e => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleProjectSelect(project.id);
              }
            }}>
            <div className="project-info">
              <div className="project-name">{project.project_name}</div>
              <div className="project-details">
                <span className="project-id">ID: {project.id}</span>
                {project.env_id && <span className="env-id">Env: {project.env_id}</span>}
              </div>
            </div>
            {selectedProjectId === project.id && <div className="selected-indicator">✓</div>}
          </button>
        ))}
      </div>
      {selectedProjectId && (
        <div className="selected-project-info">
          <p>Selected Project ID: {selectedProjectId}</p>
        </div>
      )}
    </div>
  );
};

export default ProjectSelector;
