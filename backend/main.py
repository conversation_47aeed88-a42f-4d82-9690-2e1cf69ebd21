"""
DrCode: UI Testing Backend API Server
Provides auto-refinement and vector database management functionality
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
import logging
from contextlib import asynccontextmanager
import os
import httpx
from fastapi import Request
import json
from datetime import datetime
from typing import Dict, List, Optional
import threading

from controllers.auto_refinement_controller import router as auto_refinement_router
from controllers.vector_db_controller import router as vector_db_router
from controllers.health_controller import router as health_router
from controllers.document_evaluation_controller import router as document_evaluation_router
from controllers.testcase_controller import router as testcase_router
import openai

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Token usage tracking
class TokenUsageTracker:
    def __init__(self):
        self.usage_data = []
        self.lock = threading.Lock()
    
    def add_usage(self, model: str, input_tokens: int, output_tokens: int, 
                  request_id: str, timestamp: datetime, success: bool = True):
        with self.lock:
            usage_record = {
                "request_id": request_id,
                "model": model,
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": input_tokens + output_tokens,
                "timestamp": timestamp.isoformat(),
                "success": success
            }
            self.usage_data.append(usage_record)
            logger.info(f"Token usage tracked: {usage_record}")
    
    def get_usage_stats(self, model: Optional[str] = None) -> Dict:
        with self.lock:
            if model:
                filtered_data = [record for record in self.usage_data if record["model"] == model]
            else:
                filtered_data = self.usage_data
            
            if not filtered_data:
                return {
                    "total_requests": 0,
                    "total_input_tokens": 0,
                    "total_output_tokens": 0,
                    "total_tokens": 0,
                    "successful_requests": 0,
                    "failed_requests": 0
                }
            
            total_requests = len(filtered_data)
            total_input_tokens = sum(record["input_tokens"] for record in filtered_data)
            total_output_tokens = sum(record["output_tokens"] for record in filtered_data)
            total_tokens = sum(record["total_tokens"] for record in filtered_data)
            successful_requests = sum(1 for record in filtered_data if record["success"])
            failed_requests = total_requests - successful_requests
            
            return {
                "total_requests": total_requests,
                "total_input_tokens": total_input_tokens,
                "total_output_tokens": total_output_tokens,
                "total_tokens": total_tokens,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "average_input_tokens": total_input_tokens / total_requests if total_requests > 0 else 0,
                "average_output_tokens": total_output_tokens / total_requests if total_requests > 0 else 0,
                "average_total_tokens": total_tokens / total_requests if total_requests > 0 else 0
            }
    
    def get_recent_usage(self, limit: int = 10) -> List[Dict]:
        with self.lock:
            return self.usage_data[-limit:] if self.usage_data else []
    
    def clear_usage_data(self):
        with self.lock:
            self.usage_data.clear()
            logger.info("Token usage data cleared")

# Initialize token usage tracker
token_tracker = TokenUsageTracker()

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.info("🚀 DrCode: UI Testing Backend API Server starting up...")
    yield
    # Shutdown
    logger.info("🛑 DrCode: UI Testing Backend API Server shutting down...")


# Create FastAPI app with lifespan
app = FastAPI(
    title="DrCode: UI Testing Backend API",
    description="Backend API for DrCode: UI Testing auto-refinement and vector database management",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify exact origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(health_router, prefix="/api/v1")
app.include_router(auto_refinement_router, prefix="/api/v1")
app.include_router(vector_db_router, prefix="/api/v1")
app.include_router(document_evaluation_router, prefix="/api/v1")
app.include_router(testcase_router, prefix="/api/v1")


@app.get("/")
async def root():
    return {
        "message": "Welcome to DrCode: UI Testing Backend API",
        "version": "1.0.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"Global exception handler: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": f"Internal server error: {str(exc)}"}
    )

OPENAI_API_URL = os.getenv("OPENAI_API_URL", "https://generativelanguage.googleapis.com/v1beta/openai")
DEFAULT_PLAN_MODEL = os.getenv("DEFAULT_PLAN_MODEL", "gemini-2.5-flash")
DEFAULT_NAVIGATION_MODEL = os.getenv("DEFAULT_NAVIGATION_MODEL", "gemini-2.5-flash")
DEFAULT_VALIDATION_MODEL = os.getenv("DEFAULT_VALIDATION_MODEL", "gemini-2.5-flash")
# GEMINI_PROXY_TARGET = os.getenv("GEMINI_PROXY_TARGET", "https://generativelanguage.googleapis.com/v1beta/openai/chat/completions")

@app.post("/chat/completions")
async def proxy_chat_completions(request: Request):
    import uuid
    request_id = str(uuid.uuid4())
    timestamp = datetime.now()
    
    try:
        payload = await request.json()
        original_model = payload.get("model", "unknown")
        
        if payload["model"] == "planner":
            payload["model"] = DEFAULT_PLAN_MODEL
        elif payload["model"] == "navigator":
            payload["model"] = DEFAULT_NAVIGATION_MODEL
        elif payload["model"] == "validator":
            payload["model"] = DEFAULT_VALIDATION_MODEL
        else:
            # Track failed request
            token_tracker.add_usage(
                model=original_model,
                input_tokens=0,
                output_tokens=0,
                request_id=request_id,
                timestamp=timestamp,
                success=False
            )
            return JSONResponse(status_code=400, content={"detail": "Invalid model"})
        
        payload['max_tokens'] = 10000
        
        # Calculate input tokens (rough estimation)
        input_tokens = 0
        if "messages" in payload:
            for message in payload["messages"]:
                content = message.get("content", "")
                if isinstance(content, str):
                    # Rough estimation: 1 token ≈ 4 characters
                    input_tokens += len(content) // 4
                elif isinstance(content, list):
                    # Handle multimodal content
                    for item in content:
                        if isinstance(item, dict) and item.get("type") == "text":
                            text_content = item.get("text", "")
                            input_tokens += len(text_content) // 4
        
        client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY', ''), base_url=OPENAI_API_URL)
        resp = client.chat.completions.create(**payload)
        
        # Extract output tokens from response
        output_tokens = resp.usage.completion_tokens if resp.usage else 0
        
        # Track successful request
        token_tracker.add_usage(
            model=payload["model"],
            input_tokens=input_tokens,
            output_tokens=output_tokens,
            request_id=request_id,
            timestamp=timestamp,
            success=True
        )
        
        return JSONResponse(status_code=200, content=resp.model_dump())
        
    except httpx.HTTPStatusError as e:
        logger.error(f"Proxy error: {e.response.status_code} {e.response.text}")
        # Track failed request
        token_tracker.add_usage(
            model=original_model if 'original_model' in locals() else "unknown",
            input_tokens=0,
            output_tokens=0,
            request_id=request_id,
            timestamp=timestamp,
            success=False
        )
        return JSONResponse(status_code=e.response.status_code, content={"detail": e.response.text})
    except Exception as e:
        logger.error(f"Proxy exception: {str(e)}")
        # Track failed request
        token_tracker.add_usage(
            model=original_model if 'original_model' in locals() else "unknown",
            input_tokens=0,
            output_tokens=0,
            request_id=request_id,
            timestamp=timestamp,
            success=False
        )
        return JSONResponse(status_code=500, content={"detail": f"Proxy error: {str(e)}"})


@app.get("/api/v1/token-usage")
async def get_token_usage(model: Optional[str] = None):
    """Get token usage statistics"""
    stats = token_tracker.get_usage_stats(model)
    return {
        "model": model if model else "all",
        "statistics": stats,
        "timestamp": datetime.now().isoformat()
    }


@app.get("/api/v1/token-usage/recent")
async def get_recent_token_usage(limit: int = 10):
    """Get recent token usage records"""
    recent_usage = token_tracker.get_recent_usage(limit)
    return {
        "recent_usage": recent_usage,
        "limit": limit,
        "timestamp": datetime.now().isoformat()
    }


@app.delete("/api/v1/token-usage")
async def clear_token_usage():
    """Clear all token usage data"""
    token_tracker.clear_usage_data()
    return {
        "message": "Token usage data cleared successfully",
        "timestamp": datetime.now().isoformat()
    }


if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8001,  # Different port from web-ui
        reload=True,
        log_level="info"
    )
