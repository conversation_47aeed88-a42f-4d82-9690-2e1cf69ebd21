import { loginStore } from '@extension/storage';

/**
 * Get authentication headers for API requests
 * @returns Promise with headers object containing user_id, project_id, and session_id
 */
export async function getAuthHeaders(): Promise<Record<string, string>> {
  try {
    const isLoggedIn = await loginStore.isLoggedIn();
    if (!isLoggedIn) {
      return {};
    }

    const headers = await loginStore.getHeaders();
    return {
      'x-user-id': headers.user_id,
      'x-project-id': headers.project_id.toString(),
      'x-session-id': headers.session_id,
    };
  } catch (error) {
    console.error('Failed to get auth headers:', error);
    return {};
  }
}

/**
 * Get complete headers for API requests including content type and auth headers
 * @param contentType - Content type for the request (defaults to 'application/json')
 * @returns Promise with complete headers object
 */
export async function getApiHeaders(contentType = 'application/json'): Promise<Record<string, string>> {
  const authHeaders = await getAuthHeaders();
  return {
    'Content-Type': contentType,
    ...authHeaders,
  };
}
