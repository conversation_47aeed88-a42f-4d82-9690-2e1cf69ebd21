/**
 * Vector Database Service for handling document upload and RAG functionality
 */

import { getBackendUrl } from '@extension/storage';
import { getApiHeaders, getAuthHeaders } from '@extension/shared';

export interface FileUploadResponse {
  success: boolean;
  message: string;
  file_id: string;
  filename: string;
  file_url: string;
  chunks_added: number;
  user_id: string;
  project_id: string;
}

export interface QueryResult {
  score: number;
  text: string;
  filename: string;
  file_url: string;
  file_id?: string;
  chunk_index: number;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  metadata: Record<string, any>;
}

export interface QueryResponse {
  success: boolean;
  message: string;
  results: QueryResult[];
  total_results: number;
}

export interface ProjectQueryRequest {
  query: string;
  user_id?: string;
  project_id?: string;
  top_k?: number;
}

export interface FileQueryRequest {
  query: string;
  file_id: string;
  user_id?: string;
  project_id?: string;
  top_k?: number;
}

/**
 * Upload a file to the vector database
 */
export async function uploadFile(
  file: File,
  userId: string = 'demo_user',
  projectId: string = 'demo_project',
): Promise<FileUploadResponse> {
  const backendUrl = await getBackendUrl();
  const authHeaders = await getAuthHeaders();
  const formData = new FormData();
  formData.append('file', file);
  formData.append('user_id', authHeaders['x-user-id'] || userId);
  formData.append('project_id', authHeaders['x-project-id'] || projectId);

  const response = await fetch(`${backendUrl}/api/v1/files/upload`, {
    method: 'POST',
    headers: {
      // Don't set Content-Type for FormData - let browser set it with boundary
      'x-user-id': authHeaders['x-user-id'] || userId,
      'x-project-id': authHeaders['x-project-id'] || projectId,
      'x-session-id': authHeaders['x-session-id'] || '',
    },
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Upload failed' }));
    throw new Error(errorData.detail || 'Failed to upload file');
  }

  return response.json();
}

/**
 * Query documents within a specific project
 */
export async function queryProjectDocuments(request: ProjectQueryRequest): Promise<QueryResponse> {
  const backendUrl = await getBackendUrl();
  const headers = await getApiHeaders();
  const response = await fetch(`${backendUrl}/api/v1/query/project`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      query: request.query,
      user_id: request.user_id || 'demo_user',
      project_id: request.project_id || 'demo_project',
      top_k: request.top_k || 5,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Query failed' }));
    throw new Error(errorData.detail || 'Failed to query project documents');
  }

  return response.json();
}

/**
 * Query documents within a specific file
 */
export async function queryFileDocuments(request: FileQueryRequest): Promise<QueryResponse> {
  const backendUrl = await getBackendUrl();
  const headers = await getApiHeaders();
  const response = await fetch(`${backendUrl}/api/v1/query/file`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      query: request.query,
      file_id: request.file_id,
      user_id: request.user_id || 'demo_user',
      project_id: request.project_id || 'demo_project',
      top_k: request.top_k || 5,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Query failed' }));
    throw new Error(errorData.detail || 'Failed to query file documents');
  }

  return response.json();
}

/**
 * Legacy document upload (for backward compatibility)
 */
export async function uploadDocument(file: File, metadata: Record<string, unknown> = {}): Promise<unknown> {
  const backendUrl = await getBackendUrl();
  const authHeaders = await getAuthHeaders();
  const formData = new FormData();
  formData.append('file', file);
  formData.append('metadata', JSON.stringify(metadata));

  const response = await fetch(`${backendUrl}/api/v1/documents/upload`, {
    method: 'POST',
    headers: {
      'x-user-id': authHeaders['x-user-id'] || '',
      'x-project-id': authHeaders['x-project-id'] || '',
      'x-session-id': authHeaders['x-session-id'] || '',
    },
    body: formData,
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Upload failed' }));
    throw new Error(errorData.detail || 'Failed to upload document');
  }

  return response.json();
}

/**
 * Legacy document search (for backward compatibility)
 */
export async function searchDocuments(
  query: string,
  topK: number = 5,
  filter: Record<string, unknown> = {},
): Promise<unknown> {
  const backendUrl = await getBackendUrl();
  const headers = await getApiHeaders();
  const response = await fetch(`${backendUrl}/api/v1/search`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      query,
      top_k: topK,
      filter_dict: filter,
    }),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Search failed' }));
    throw new Error(errorData.detail || 'Failed to search documents');
  }

  return response.json();
}
