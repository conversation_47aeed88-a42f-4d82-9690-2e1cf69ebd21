import { StorageEnum } from '../base/enums';
import { createStorage } from '../base/base';
import type { BaseStorage } from '../base/types';

// Interface for project data
export interface Project {
  id: number;
  project_name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
  env_id: number | null;
  client_name: string | null;
}

// Interface for DrCode login data
export interface DrCodeLoginData {
  sessionId: string;
  userData: {
    user_id: string;
    username: string;
    name: string;
    carrier: string;
    orgs: Array<{
      id: string;
      org_name: string;
      avatar_url: string;
    }>;
  };
  selectedProjectId?: number;
  projects?: Project[];
  isLoggedIn: boolean;
  lastLoginTime?: number;
}

export type LoginStorage = BaseStorage<DrCodeLoginData> & {
  setLoginData: (sessionId: string, userData: DrCodeLoginData['userData']) => Promise<void>;
  setProjects: (projects: Project[]) => Promise<void>;
  setSelectedProject: (projectId: number) => Promise<void>;
  clearLoginData: () => Promise<void>;
  getLoginData: () => Promise<DrCodeLoginData>;
  isLoggedIn: () => Promise<boolean>;
  getSessionId: () => Promise<string | null>;
  getUserData: () => Promise<DrCodeLoginData['userData'] | null>;
  getProjects: () => Promise<Project[]>;
  getSelectedProject: () => Promise<Project | null>;
  getSelectedProjectId: () => Promise<number | null>;
  getHeaders: () => Promise<{ user_id: string; session_id: string; project_id: number }>;
};

// Default login data
export const DEFAULT_LOGIN_DATA: DrCodeLoginData = {
  sessionId: '',
  userData: {
    user_id: '',
    username: '',
    name: '',
    carrier: '',
    orgs: [],
  },
  isLoggedIn: false,
};

const storage = createStorage<DrCodeLoginData>('user', DEFAULT_LOGIN_DATA, {
  storageEnum: StorageEnum.Local,
  liveUpdate: true,
});

export const loginStore: LoginStorage = {
  ...storage,

  async setLoginData(sessionId: string, userData: DrCodeLoginData['userData']) {
    const loginData: DrCodeLoginData = {
      sessionId,
      userData,
      isLoggedIn: true,
      lastLoginTime: Date.now(),
    };
    await storage.set(loginData);
  },

  async setProjects(projects: Project[]) {
    const currentData = await this.getLoginData();
    await storage.set({
      ...currentData,
      projects,
    });
  },

  async setSelectedProject(projectId: number) {
    const currentData = await this.getLoginData();
    await storage.set({
      ...currentData,
      selectedProjectId: projectId,
    });
  },

  async clearLoginData() {
    await storage.set(DEFAULT_LOGIN_DATA);
  },

  async getLoginData() {
    const data = await storage.get();
    return data || DEFAULT_LOGIN_DATA;
  },

  async isLoggedIn() {
    const data = await this.getLoginData();
    return data.isLoggedIn && !!data.sessionId;
  },

  async getSessionId() {
    const data = await this.getLoginData();
    return data.isLoggedIn ? data.sessionId : null;
  },

  async getUserData() {
    const data = await this.getLoginData();
    return data.isLoggedIn ? data.userData : null;
  },

  async getProjects() {
    const data = await this.getLoginData();
    return data.projects || [];
  },

  async getSelectedProject() {
    const data = await this.getLoginData();
    if (!data.selectedProjectId || !data.projects) {
      return null;
    }
    return data.projects.find(project => project.id === data.selectedProjectId) || null;
  },

  async getSelectedProjectId() {
    const data = await this.getLoginData();
    return data.selectedProjectId || null;
  },

  async getHeaders() {
    const data = await this.getLoginData();
    const selectedProject = await this.getSelectedProject();
    return {
      user_id: data.userData.user_id,
      session_id: data.sessionId,
      project_id: selectedProject?.id || data.projects?.[0]?.id || 0,
    };
  },
};
