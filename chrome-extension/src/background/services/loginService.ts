import { loginStore, type DrCodeLoginData } from '@extension/storage';

export class LoginService {
  private static instance: LoginService;

  private constructor() {}

  public static getInstance(): LoginService {
    if (!LoginService.instance) {
      LoginService.instance = new LoginService();
    }
    return LoginService.instance;
  }

  /**
   * Opens the DrCode login page in a new tab
   */
  async openLoginPage(): Promise<chrome.tabs.Tab | undefined> {
    try {
      const tab = await chrome.tabs.create({
        url: 'https://www.drcode.ai/login',
        active: true,
      });
      return tab;
    } catch (error) {
      console.error('Failed to open login page:', error);
      throw error;
    }
  }

  /**
   * Extracts login data from localStorage of the DrCode page
   */
  async extractLoginData(tabId: number): Promise<{ sessionId: string; userData: DrCodeLoginData['userData'] } | null> {
    try {
      const results = await chrome.scripting.executeScript({
        target: { tabId },
        func: () => {
          const sessionId = localStorage.getItem('sessionId');
          const userDataStr = localStorage.getItem('userData');

          if (!sessionId || !userDataStr) {
            return null;
          }

          try {
            const userData = JSON.parse(userDataStr);
            return { sessionId, userData };
          } catch (error) {
            console.error('Failed to parse userData:', error);
            return null;
          }
        },
      });

      const result = results[0]?.result;
      return result || null;
    } catch (error) {
      console.error('Failed to extract login data:', error);
      return null;
    }
  }

  /**
   * Stores login data in extension storage
   */
  async storeLoginData(sessionId: string, userData: DrCodeLoginData['userData']): Promise<void> {
    try {
      await loginStore.setLoginData(sessionId, userData);
      console.log('Login data stored successfully');
    } catch (error) {
      console.error('Failed to store login data:', error);
      throw error;
    }
  }

  /**
   * Checks if user is logged in
   */
  async isLoggedIn(): Promise<boolean> {
    return await loginStore.isLoggedIn();
  }

  /**
   * Gets current session ID
   */
  async getSessionId(): Promise<string | null> {
    return await loginStore.getSessionId();
  }

  /**
   * Gets current user data
   */
  async getUserData(): Promise<DrCodeLoginData['userData'] | null> {
    return await loginStore.getUserData();
  }

  /**
   * Logs out the user
   */
  async logout(): Promise<void> {
    try {
      await loginStore.clearLoginData();
      console.log('User logged out successfully');
    } catch (error) {
      console.error('Failed to logout:', error);
      throw error;
    }
  }

  /**
   * Monitors a tab for login completion and extracts data
   */
  async monitorLoginCompletion(tabId: number): Promise<void> {
    const maxAttempts = 30; // 30 seconds
    let attempts = 0;

    const checkLogin = async (): Promise<void> => {
      attempts++;

      const loginData = await this.extractLoginData(tabId);

      if (loginData) {
        await this.storeLoginData(loginData.sessionId, loginData.userData);
        console.log('Login completed successfully');
        return;
      }

      if (attempts >= maxAttempts) {
        console.log('Login monitoring timed out');
        return;
      }

      // Check again in 1 second
      setTimeout(checkLogin, 1000);
    };

    // Start monitoring after a short delay
    setTimeout(checkLogin, 1000);
  }
}

export const loginService = LoginService.getInstance();
