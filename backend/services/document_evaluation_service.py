"""
Document Evaluation Service using Gemini models via OpenAI-compatible API
"""

import os
import json
import logging
from typing import Dict, Any
from openai import OpenAI
from pydantic import BaseModel
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

logger = logging.getLogger(__name__)


class DocumentEvaluationService:
    """Service for evaluating documents using Gemini models via OpenAI-compatible endpoint"""
    
    def __init__(self):
        # Initialize client for Gemini via OpenAI-compatible endpoint
        api_key = os.getenv("OPENAI_API_KEY")  # This contains the Gemini API key
        api_url = os.getenv("OPENAI_API_URL", "https://generativelanguage.googleapis.com/v1beta/openai")

        if not api_key:
            logger.warning("OPENAI_API_KEY (Gemini API key) not found in environment variables")
            self.client = None
        else:
            self.client = OpenAI(api_key=api_key, base_url=api_url)
    
    def evaluate_document(self, document_text: str) -> Dict[str, Any]:
        """
        Evaluate a document based on the 5 criteria for autonomous UI testing
        
        Args:
            document_text: The document content to evaluate
            
        Returns:
            Dict containing the evaluation results in JSON format
        """
        if not self.client:
            return {
                "error": "OpenAI client not initialized",
                "details": "Please set OPENAI_API_KEY environment variable and restart the server"
            }
        
        # Validate input
        if not document_text or not document_text.strip():
            return {
                "error": "Document text is empty or invalid",
                "details": "Please provide valid document content for evaluation"
            }
        
        # Construct the evaluation prompt
        prompt = self._build_evaluation_prompt(document_text)

        try:
            model = os.getenv("DOCUMENT_EVALUATION_MODEL", "gemini-2.5-flash")
            logger.info(f"Making API call to Gemini with model: {model}")
            logger.info(f"API URL: {os.getenv('OPENAI_API_URL', 'https://generativelanguage.googleapis.com/v1beta/openai')}")

            response = self.client.chat.completions.create(
                model=model,  # Using Gemini model via OpenAI-compatible endpoint
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert technical evaluator for autonomous UI testing systems. You must return only valid JSON, no additional text, no markdown formatting, no code blocks, and no explanation. Return raw JSON only."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1
            )

            logger.info(f"API response received: {response}")
            logger.info(f"Response choices: {len(response.choices) if response.choices else 0}")
            
            # Extract and parse the response - handle potential null content
            if not response.choices or len(response.choices) == 0:
                logger.error("No choices in API response")
                return {
                    "error": "No choices in API response",
                    "details": "The API response contained no choices"
                }

            response_content = response.choices[0].message.content
            logger.info(f"Response content: {response_content}")

            if response_content is None:
                logger.error("Received null response from Gemini API")
                logger.error(f"Full response object: {response}")
                logger.error(f"Message object: {response.choices[0].message}")
                return {
                    "error": "Null response from Gemini API",
                    "details": "The API returned an empty response. This might be due to content filtering or an invalid model name."
                }
            
            response_text = response_content.strip()
            
            # Clean up markdown formatting if present
            if response_text.startswith('```json'):
                # Remove markdown code block formatting
                response_text = response_text.replace('```json', '').replace('```', '').strip()
            elif response_text.startswith('```'):
                # Remove any other code block formatting
                response_text = response_text.replace('```', '').strip()
            
            # Try to parse as JSON
            try:
                evaluation_result = json.loads(response_text)
                return evaluation_result
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse Gemini response as JSON: {e}")
                logger.error(f"Response text: {response_text}")
                # Return a fallback response
                return {
                    "error": "Failed to parse evaluation results",
                    "raw_response": response_text
                }
                
        except Exception as e:
            logger.error(f"Error calling Gemini API: {str(e)}")
            # Return a proper error response instead of raising exception
            return {
                "error": f"Gemini API error: {str(e)}",
                "details": "Please check your Gemini API key and internet connection"
            }
    
    def _build_evaluation_prompt(self, document_text: str) -> str:
        """
        Build the evaluation prompt for the document
        
        Args:
            document_text: The document content to evaluate
            
        Returns:
            The formatted prompt string
        """
        prompt = f"""You are an expert technical evaluator for autonomous UI testing systems.

Given the following product documentation delimited by triple backticks, analyze the quality and completeness of the document with respect to enabling an AI agent to autonomously test the UI of the described website.

```{document_text}```

---

Your task is to evaluate each *user flow* described in the document along the following 5 dimensions. Use strict, calibrated scoring that reflects real-world agent performance:

Evaluation Criteria:

1. UI Element Mapping
   How clearly and consistently UI elements are described (e.g., buttons, input fields, labels, dropdowns).
   Assess clarity, naming consistency, uniqueness of selectors, and specificity of elements mentioned.

2. Flow Coverage
   How comprehensively each user flow is described.
   Include complete sequences, decision branches, edge cases, and failure modes where applicable.

3. Action-Intent Mapping
   Whether user actions are linked to their functional or business intent.
   Example: "Click 'Submit' to finalize the order and trigger backend validation."

4. Structure & Consistency
   Whether the document is well-structured, chunkable, and consistently formatted.
   Look for headings, numbered steps, and section clarity.

5. Ambiguity / Noise
   Evaluate if the document avoids vague language, contradictions, or irrelevant details.
   High score = precise, unambiguous, and task-relevant.

---

Scoring System:

Use qualitative scores that reflect real-world success rates of autonomous agents:

**Bad**: Insufficient for reliable automation. Missing critical information, vague descriptions, or major gaps that would prevent an agent from completing the flow successfully.

**Fair**: Bare minimum quality needed for agent to complete the flows with significant difficulty. Some key information present but lacks detail or has ambiguities that could cause failures.

**Good**: High quality documentation. Most edge cases, flows, and UI states are navigable. Minor gaps or ambiguities that don't significantly impact automation success.

**Excellent**: Exceptional quality. Full coverage, no ambiguity, highly actionable. An autonomous agent could reliably complete all described flows with minimal issues.

A user flow is a sequence of user actions toward a defined goal (e.g., “Login”, “Search”, “Checkout”). Use section headers or task groupings to infer flows. Maintain flow order from the document.

- Each category receives a qualitative score: Bad, Fair, Good, or Excellent
- `overall_score` is a qualitative assessment based on the average quality across all flows
- **Recommendations must be specific, actionable improvements that directly address the scoring criteria**

**Recommendation Guidelines:**
For each flow, provide recommendations that specifically target the areas that received lower scores:

- **UI Element Mapping improvements**: Suggest adding specific selectors, IDs, or element descriptions that are missing
- **Flow Coverage improvements**: Identify missing steps, edge cases, error states, or decision branches that should be documented
- **Action-Intent Mapping improvements**: Recommend adding explanations of what each action accomplishes and why it's performed
- **Structure improvements**: Suggest formatting changes, adding headings, numbering steps, or reorganizing content
- **Ambiguity/Noise improvements**: Point out specific vague language, contradictions, or irrelevant details to remove or clarify

Each recommendation should be implementable and directly lead to a higher score in the corresponding category.

Only return the JSON. No explanation outside of it.

---

Output Format:

Return only a JSON object in the following format:

```json
{{
  "overall_score": "Good",
  "flow_scores": [
    {{
      "flow_name": "Login Flow",
      "score": "Fair",
      "ui_element_mapping": "Good",
      "flow_coverage": "Fair",
      "action_intent_mapping": "Good",
      "structure": "Fair",
      "ambiguity_noise": "Bad",
      "recommendations": [
        "Add specific CSS selectors or IDs for the login button (e.g., '#login-btn' or '[data-testid=\"login-submit\"]')",
        "Document the success redirect URL and any loading states between login and redirect",
        "Include error message text and element selectors for failed login attempts",
        "Add step-by-step numbered instructions instead of paragraph descriptions"
      ]
    }},
    {{
      "flow_name": "Checkout Flow",
      "score": "Good",
      "ui_element_mapping": "Good",
      "flow_coverage": "Fair",
      "action_intent_mapping": "Excellent",
      "structure": "Good",
      "ambiguity_noise": "Excellent",
      "recommendations": [
        "Document the payment method selection process and available options",
        "Add error handling steps for declined payments or network failures",
        "Include confirmation page elements and success indicators after purchase completion"
      ]
    }}
  ],
  "global_recommendations": [
    "Add data-testid attributes or unique CSS selectors for all interactive elements (buttons, inputs, links)",
    "Document error states and recovery paths for each user flow (network failures, validation errors, timeouts)",
    "Convert all paragraph descriptions into numbered step-by-step instructions with clear action verbs",
    "Include loading states, confirmation messages, and success/failure indicators for each major action",
    "Add section headers to clearly separate different user flows and make the document more scannable"
  ]
}}
```

NOTE: The sample JSON above is only a formatting reference. Do not reuse the qualitative scores or recommendations — generate all content based on the input document provided.

Only return valid JSON with no text before or after it."""

        return prompt
